'use client';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import Link from 'next/link';
import { SearchInput, Tabs } from '@/components';
import useAuth from '@/hooks/useAuth';
import Filter from '@assets/svg/Filter';
import { fetchJobsForApplicantAPI } from '@/networks/jobs';
import type {
  FetchJobsForApplicantQueryI,
  FetchJobsForApplicantResultItemI,
  ApplicantsStatusI,
} from '@/networks/jobs/types';
import { SuggestedJobItemI, SuggestedJobsPropsI, SuggestedJobsTabI } from './types';

const statusFromTab = (tab: SuggestedJobsTabI['id']): ApplicantsStatusI => {
  return tab;
};

const formatDate = (iso: string): string => {
  try {
    const d = new Date(iso);
    return d.toLocaleDateString();
  } catch {
    return '';
  }
};

const SuggestedJobs: React.FC<SuggestedJobsPropsI> = () => {
  const tabs: SuggestedJobsTabI[] = [
    { id: "SUGGUESTED", label: 'Suggested' },
    { id: 'PENDING', label: 'Applied' },
    { id: 'SHORTLISTED', label: 'Shortlisted' },
    { id: 'ACCEPTED_BY_APPLICANT', label: 'Accepted' },
    { id: 'REJECTED_BY_ENTITY', label: 'Rejected' },
    { id: 'OFFERED', label: 'Offered' },
  ];

  const [activeTab, setActiveTab] =
    useState<SuggestedJobsTabI['id']>('SUGGUESTED');
  const [search, setSearch] = useState('');

  const [items, setItems] = useState<FetchJobsForApplicantResultItemI[]>([]);
  const [nextCursorId, setNextCursorId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const fetchJobs = useCallback(
    async (isLoadMore = false) => {
      try {
        if (isLoadMore) {
          if (!hasMore || !nextCursorId) return;
          setLoadingMore(true);
        } else {
          setLoading(true);
        }

        if (activeTab === 'SUGGUESTED') {
            


        } else {
          const baseQuery: FetchJobsForApplicantQueryI = {
            cursorId: isLoadMore && nextCursorId ? String(nextCursorId) : null,
            pageSize: 10,
            status: statusFromTab(activeTab),
          };
          const res = await fetchJobsForApplicantAPI(baseQuery);
          setItems(prev => (isLoadMore ? [...prev, ...res.data] : res.data));
          setNextCursorId(res.nextCursorId);
          setHasMore(!!res.nextCursorId && res.data.length > 0);
        }




      } catch (e) {
        console.error('Failed to fetch suggested jobs', e);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [activeTab, nextCursorId, hasMore]
  );

  useEffect(() => {
    setItems([]);
    setNextCursorId(null);
    setHasMore(true);
    fetchJobs(false);
  }, [activeTab]);

  useEffect(() => {
    const el = loadMoreRef.current;
    if (!el) return;
    const observer = new IntersectionObserver(
      entries => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !loading && !loadingMore) {
          fetchJobs(true);
        }
      },
      { root: null, rootMargin: '100px', threshold: 0.1 }
    );
    observer.observe(el);
    return () => {
      observer.disconnect();
    };
  }, [hasMore, loading, loadingMore]);

  const jobs: SuggestedJobItemI[] = useMemo(() => {
    const mapped = items.map(j => ({
      id: j.id,
      slug: j.id,
      title: j.designation?.name || 'Job',
      company: j.entity?.name || '—',
      postedAt: j.createdAt ? formatDate(j.createdAt) : '',
      avatarUrl: null,
    }));

    const s = search.trim().toLowerCase();
    if (!s) return mapped;
    return mapped.filter(
      j =>
        j.title.toLowerCase().includes(s) || j.company.toLowerCase().includes(s)
    );
  }, [items, search]);

  const { isAuthenticated } = useAuth();

  return (
    <section className="lg:col-span-6 col-span-1">
      <div className="space-y-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-bold text-xl">Suggested Jobs</h3>
          {isAuthenticated && (
            <Link
              className="text-primary font-medium text-base"
              href={'/myjobs'}
            >
              My Jobs
            </Link>
          )}
        </div>
        <div className="bg-white border border-gray-200 rounded-2xl shadow-sm overflow-hidden">
          <div className="px-4 pt-4">
            <Tabs
              items={tabs}
              activeId={activeTab}
              onChange={id => setActiveTab(id as SuggestedJobsTabI['id'])}
            />
          </div>

          <div className="px-4 py-3">
            <SearchInput
              value={search}
              onChange={e => setSearch(e.target.value)}
              rightSlot={<Filter />}
            />
          </div>

          {loading && items.length === 0 ? (
            <div className="px-4 py-12 text-center text-gray-500">
              Loading jobs…
            </div>
          ) : jobs.length === 0 ? (
            <div className="px-4 py-12 text-center text-gray-500">
              No results found
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {jobs.map(job => (
                <li key={`${activeTab}-${job.id}`} className="px-4">
                  <Link
                    href={`/jobs/${job.slug}`}
                    className="block hover:bg-gray-50 rounded-lg"
                  >
                    <div className="flex gap-4 py-4">
                      <div className="h-8 w-8 rounded-full overflow-hidden flex-shrink-0 bg-gray-100" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-semibold text-gray-900 truncate">
                          {job.title}
                        </p>
                        <p className="text-sm text-gray-700 truncate">
                          {job.company}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {job.postedAt}
                        </p>
                      </div>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          )}

          <div ref={loadMoreRef} />
          {loadingMore && (
            <div className="px-4 py-4 text-sm text-gray-500">Loading more…</div>
          )}
        </div>
      </div>
    </section>
  );
};

export default SuggestedJobs;
